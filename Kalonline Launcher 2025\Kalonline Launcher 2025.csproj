<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{5B185128-AA39-43CC-A2C3-413683DB9E74}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>Kalonline_Launcher_2025</RootNamespace>
    <AssemblyName>Project 404 Launcher</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>404 logo - nobg.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DotNetZip, Version=********, Culture=neutral, PublicKeyToken=6583c7c814667745, processorArchitecture=MSIL">
      <HintPath>..\packages\DotNetZip.1.16.0\lib\net40\DotNetZip.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="UpdateManager.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\404 logo - nobg.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\1.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\11.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\kal13_1920x10801.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\kal15_1920x10801.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\kal16_1920x10801.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\kal18_1920x10801.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\kal19_1920x10801.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\kal20_1920x10801.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\kal21_1920x10801.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\kal22_1920x10801.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\kal23_1920x10801.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\box.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\box2.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\box3.png" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="404 logo - nobg.ico" />
    <None Include="Resources\BTN6_over.png" />
    <None Include="Resources\BTN6.png" />
    <None Include="Resources\BTN4_ON_OVER.png" />
    <None Include="Resources\BTN4_ON.png" />
    <None Include="Resources\BTN3_ON_OVER.png" />
    <None Include="Resources\BTN3_ON.png" />
    <None Include="Resources\BTN2_ON_OVER.png" />
    <None Include="Resources\BTN2_ON.png" />
    <None Include="Resources\BTN_ON_OVER.png" />
    <None Include="Resources\BTN_ON.png" />
    <None Include="Resources\BTN_OFF.png" />
    <None Include="Resources\B01-off.bmp" />
    <None Include="Resources\B01-default.bmp" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>