using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;

public class MD5FileEntry
{
    public string Path { get; set; }
    public string MD5Hash { get; set; }
}

public class MD5Test
{
    public static void Main()
    {
        // Test MD5.txt parsing
        string testContent = @"((path ""data/config/config.pk"") (MD5 ""ABC123DEF456789""))
((path ""engine.exe"") (MD5 ""DEF456ABC123789""))
((path ""engine.dll"") (MD5 ""789ABC123DEF456""))";

        var entries = ParseMD5File(testContent);
        
        Console.WriteLine("Parsed MD5 entries:");
        foreach (var entry in entries)
        {
            Console.WriteLine($"Path: {entry.Path}, MD5: {entry.MD5Hash}");
        }
        
        Console.WriteLine("\nPress any key to exit...");
        Console.ReadKey();
    }
    
    private static List<MD5FileEntry> ParseMD5File(string content)
    {
        var entries = new List<MD5FileEntry>();
        
        // Parse format: ((path "data/config/config.pk") (MD5 "the md5 of config.exe"))
        var regex = new Regex(@"\(\(path\s+""([^""]+)""\)\s+\(MD5\s+""([^""]+)""\)\)", RegexOptions.IgnoreCase);
        var matches = regex.Matches(content);

        foreach (Match match in matches)
        {
            if (match.Groups.Count >= 3)
            {
                entries.Add(new MD5FileEntry
                {
                    Path = match.Groups[1].Value.Replace("/", "\\"), // Convert to Windows path separators
                    MD5Hash = match.Groups[2].Value.ToUpperInvariant()
                });
            }
        }

        return entries;
    }
}
