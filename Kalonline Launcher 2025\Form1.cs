﻿using System;
using System.Diagnostics;
using System.IO;
using System.Reflection.Emit;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Kalonline_Launcher_2025
{
    public partial class Form1 : Form
    {
        private readonly string engineExe = "engine.exe";
        private readonly UpdateManager updateManager;
        private bool isUpdateInProgress = false;
        private bool hasCheckedForUpdates = false;

        public Form1()
        {
            InitializeComponent();
            updateManager = new UpdateManager();
            SetupEventHandlers();
            SetupClientsTxtValidation();

            versionLBL.Parent = pictureBoxBackground;
            versionLBL.BackColor = System.Drawing.Color.Transparent; // Make version label background transparent
            titleLBL.Parent = pictureBoxBackground;
            titleLBL.BackColor = System.Drawing.Color.Transparent; // Make title label background transparent
            infoLBL.Parent = pictureBoxBackground;
            infoLBL.BackColor = System.Drawing.Color.Transparent; // Make info label background transparent
            statusLBL.Parent = pictureBoxBackground;
            statusLBL.BackColor = System.Drawing.Color.Transparent; // Make status label background transparent
            msgLBL.Parent = pictureBox2;
            msgLBL.BackColor = System.Drawing.Color.Transparent; // Make message label background transparent
            pictureBox1.Parent = pictureBoxBackground;
            pictureBox1.BackColor = System.Drawing.Color.Transparent; // Make picture box background transparent
            pictureBox2.Parent = pictureBoxBackground;
            pictureBox2.BackColor = System.Drawing.Color.Transparent; // Make picture box background transparent
            label1.Parent = pictureBoxBackground;
            label1.BackColor = System.Drawing.Color.Transparent; // Make status label background transparent
            launchBTN.Parent = pictureBoxBackground;
            launchBTN.BackColor = System.Drawing.Color.Transparent; // Make launch button background transparent
            updateBTN.Parent = pictureBoxBackground;
            updateBTN.BackColor = System.Drawing.Color.Transparent; // Make launch button background transparent



        }

        private readonly System.Drawing.Image[] backgroundImages = new System.Drawing.Image[]
        {
            Properties.Resources.kal21_1920x10801,
            Properties.Resources.kal13_1920x10801,
            Properties.Resources.kal19_1920x10801,
            Properties.Resources.kal15_1920x10801,
            Properties.Resources.kal16_1920x10801,
            Properties.Resources.kal18_1920x10801,
            Properties.Resources.kal23_1920x10801,
            Properties.Resources.kal22_1920x10801
        };

        private void SetupEventHandlers()
        {
            updateManager.ProgressChanged += OnUpdateProgress;
            updateManager.StatusChanged += OnStatusChanged;
            updateManager.UpdateCompleted += OnUpdateCompleted;
        }

        private void SetupClientsTxtValidation()
        {
            // Only allow numeric input in clientsTXT
            clientsTXT.KeyPress += (sender, e) =>
            {
                // Allow control keys (backspace, delete, etc.)
                if (char.IsControl(e.KeyChar))
                    return;

                // Allow only digits
                if (!char.IsDigit(e.KeyChar))
                {
                    e.Handled = true;
                }
            };

            // Prevent pasting non-numeric content
            clientsTXT.TextChanged += (sender, e) =>
            {
                string text = clientsTXT.Text;
                string numericText = "";

                foreach (char c in text)
                {
                    if (char.IsDigit(c))
                        numericText += c;
                }

                if (text != numericText)
                {
                    int selectionStart = clientsTXT.SelectionStart;
                    clientsTXT.Text = numericText;
                    clientsTXT.SelectionStart = Math.Min(selectionStart, numericText.Length);
                }

                // Ensure minimum value of 1
                if (string.IsNullOrEmpty(clientsTXT.Text) || clientsTXT.Text == "0")
                {
                    clientsTXT.Text = "1";
                    clientsTXT.SelectionStart = clientsTXT.Text.Length;
                }
            };
        }

        private async void Form1_Load(object sender, EventArgs e)
        {
            // UI loads instantly, then check for updates in background
            SetStatus("Initializing...");
            UpdateVersionDisplay();
            var rand = new Random();
            pictureBoxBackground.Image = backgroundImages[rand.Next(backgroundImages.Length)];

            // Start background tasks
            await Task.Run(async () =>
            {
                await Task.Delay(500); // Small delay to let UI fully load
                await CheckForUpdatesAsync();
                await LoadServerInfoAsync();
            });
        }

        private void UpdateVersionDisplay()
        {
            string localVersion = GetLocalVersion();
            versionLBL.Text = $"Current Version: {localVersion}";
        }

        private string GetLocalVersion()
        {
            string versionPath = "version.txt";
            return File.Exists(versionPath) ? File.ReadAllText(versionPath).Trim() : "1.0.0";
        }

        private async Task CheckForUpdatesAsync()
        {
            if (hasCheckedForUpdates) return;

            try
            {
                var result = await updateManager.CheckForUpdatesAsync();
                hasCheckedForUpdates = true;

                if (result.Success)
                {
                    if (result.UpdateRequired)
                    {
                        this.Invoke((MethodInvoker)delegate
                        {
                            launchBTN.Enabled = false;
                            updateBTN.Text = "Update Available";
                            updateBTN.BackgroundImage = Properties.Resources.BTN4_ON;
                            updateBTN.ForeColor = System.Drawing.Color.NavajoWhite;
                            SetStatus($"Update available: v{result.ServerVersion}");
                        });
                    }
                    else
                    {
                        this.Invoke((MethodInvoker)delegate
                        {
                            launchBTN.Enabled = true;
                            SetStatus("Game is up to date");
                        });
                    }
                }
                else
                {
                    this.Invoke((MethodInvoker)delegate
                    {
                        SetStatus("Could not check for updates");
                    });
                }
            }
            catch (Exception ex)
            {
                this.Invoke((MethodInvoker)delegate
                {
                    SetStatus($"Update check failed: {ex.Message}");
                });
            }
        }

        private async Task LoadServerInfoAsync()
        {
            try
            {
                string serverInfo = await updateManager.GetServerInfoAsync();

                this.Invoke((MethodInvoker)delegate
                {
                    msgLBL.Text = serverInfo;
                });
            }
            catch (Exception ex)
            {
                // If loading server info fails, keep the default message
                // No need to show error to user as this is not critical
            }
        }

        private async void updateBTN_Click(object sender, EventArgs e)
        {
            if (isUpdateInProgress) return;

            try
            {
                isUpdateInProgress = true;
                updateBTN.Enabled = false;
                launchBTN.Enabled = false;
                progressBar.Visible = true;

                var checkResult = await updateManager.CheckForUpdatesAsync();

                if (checkResult.Success && checkResult.UpdateRequired)
                {
                    bool success = await updateManager.DownloadAndInstallUpdateAsync();

                    if (success)
                    {
                        UpdateVersionDisplay();
                        updateBTN.Text = "Check Update";
                        updateBTN.BackgroundImage = Properties.Resources.BTN3_ON;
                        updateBTN.ForeColor = System.Drawing.Color.FromArgb(192, 192, 255);
                        //updateBTN.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(192, 192, 255);
                    }
                }
                else if (checkResult.Success)
                {
                    SetStatus("No updates available");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Update failed: {ex.Message}", "Update Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                isUpdateInProgress = false;
                updateBTN.Enabled = true;
                launchBTN.Enabled = true;
                progressBar.Visible = false;
                infoLBL.Text = "";
                progressBar.Value = 0;
            }
        }

        private async void launchBTN_Click(object sender, EventArgs e)
        {
            if (!File.Exists(engineExe))
            {
                MessageBox.Show("engine.exe not found!\n\nPlease make sure the launcher is in the same directory as the game files.",
                    "Game Not Found", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // Parse number of clients from textbox
            if (!int.TryParse(clientsTXT.Text, out int numberOfClients) || numberOfClients < 1 || numberOfClients > 15)
            {
                MessageBox.Show("Please enter a valid number of clients (1 or greater, and less than 15).", "Invalid Input",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                clientsTXT.Text = "15"; // Reset to default value
                return;
            }

            // Disable launch button during the process
            launchBTN.Enabled = false;
            launchBTN.Text = "Launching...";

            try
            {
                SetStatus($"Launching {numberOfClients} client(s)...");

                for (int i = 1; i <= numberOfClients; i++)
                {
                    try
                    {
                        Process.Start(engineExe);
                        SetStatus($"Launched client {i} of {numberOfClients}");

                        // Add delay between launches (except for the last one)
                        if (i < numberOfClients)
                        {
                            SetStatus($"Waiting 5 seconds before launching client {i + 1}...");
                            await Task.Delay(8000); // 8 second delay
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Failed to launch client {i}: {ex.Message}", "Launch Error",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                        break; // Stop launching if one fails
                    }
                }

                SetStatus($"Successfully launched {numberOfClients} client(s)");

                // Wait a moment to show the completion message, then close launcher
                await Task.Delay(2000);
                Application.Exit();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"An error occurred during launch: {ex.Message}", "Launch Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Re-enable launch button in case of error
                launchBTN.Enabled = true;
                launchBTN.Text = "Launch Game";
            }
        }

        private void websiteBTN_Click(object sender, EventArgs e)
        {
            try
            {
                Process.Start("https://www.kalonline.com"); // Replace with actual website
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to open website: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void discordBTN_Click(object sender, EventArgs e)
        {
            try
            {
                Process.Start("https://discord.gg/kalonline"); // Replace with actual Discord invite
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to open Discord: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OnUpdateProgress(object sender, UpdateProgressEventArgs e)
        {
            if (this.InvokeRequired)
            {
                this.Invoke((MethodInvoker)delegate { OnUpdateProgress(sender, e); });
                return;
            }

            progressBar.Value = e.Percentage;
            infoLBL.Text = e.Message;
        }

        private void OnStatusChanged(object sender, UpdateStatusEventArgs e)
        {
            if (this.InvokeRequired)
            {
                this.Invoke((MethodInvoker)delegate { OnStatusChanged(sender, e); });
                return;
            }

            SetStatus(e.Status);
        }

        private void OnUpdateCompleted(object sender, UpdateCompletedEventArgs e)
        {
            if (this.InvokeRequired)
            {
                this.Invoke((MethodInvoker)delegate { OnUpdateCompleted(sender, e); });
                return;
            }

            if (e.Success)
            {
                SetStatus("Update completed successfully!");
                richTextBox1.AppendText($"\n\n[{DateTime.Now:HH:mm:ss}] Update completed successfully!");

                           }
            else
            {
                SetStatus($"Update failed: {e.ErrorMessage}");
                richTextBox1.AppendText($"\n\n[{DateTime.Now:HH:mm:ss}] Update failed: {e.ErrorMessage}");

            }
        }

        private void SetStatus(string status)
        {
            statusLBL.Text = status;
            richTextBox1.AppendText($"\n[{DateTime.Now:HH:mm:ss}] {status}");
            richTextBox1.ScrollToCaret();
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            // Clean up event handlers
            if (updateManager != null)
            {
                updateManager.ProgressChanged -= OnUpdateProgress;
                updateManager.StatusChanged -= OnStatusChanged;
                updateManager.UpdateCompleted -= OnUpdateCompleted;
            }
            base.OnFormClosed(e);
        }

        private void pictureBoxBackground_Click(object sender, EventArgs e)
        {

        }

        private void launchBTN_MouseHover(object sender, EventArgs e)
        {
            //launchBTN.BackColor = System.Drawing.Color.Transparent;
        }

        private void launchBTN_MouseEnter(object sender, EventArgs e)
        {
            launchBTN.BackgroundImage = Properties.Resources.BTN_ON_OVER;
        }

        private void launchBTN_MouseLeave(object sender, EventArgs e)
        {
            launchBTN.BackgroundImage = Properties.Resources.BTN_ON;
        }

        private void clientsTXT_TextChanged(object sender, EventArgs e)
        {

        }

        private void updateBTN_MouseEnter(object sender, EventArgs e)
        {
            
            if (updateBTN.Text == "Update Available")
            {
                updateBTN.BackgroundImage = Properties.Resources.BTN4_ON_OVER;
            }
            else
            {
                updateBTN.BackgroundImage = Properties.Resources.BTN3_ON_OVER;
            }
                
        }

        private void updateBTN_MouseLeave(object sender, EventArgs e)
        {
            if (updateBTN.Text == "Update Available")
            {
                updateBTN.BackgroundImage = Properties.Resources.BTN4_ON;
            }
            else
            {
                updateBTN.BackgroundImage = Properties.Resources.BTN3_ON;
            }

        }

        private void launchBTN_MouseClick(object sender, MouseEventArgs e)
        {
           
        }
    }
}