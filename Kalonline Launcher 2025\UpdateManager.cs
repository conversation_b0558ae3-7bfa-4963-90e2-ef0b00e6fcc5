using System;
using System.IO;
using System.Net;
using System.Threading.Tasks;
using System.ComponentModel;
using Ionic.Zip;

namespace Kalonline_Launcher_2025
{
    public class UpdateManager
    {
        private readonly string serverVersionUrl = "http://*************/updater/version.txt";
        private readonly string serverInfoUrl = "http://*************/updater/info.txt";
        private readonly string serverZipUrl = "http://*************/updater/update.zip";
        private readonly string localVersionPath = "version.txt";
        private readonly string localZipPath = "update_.zip";
        private readonly string versionCachePath = "version_cache.txt";
        private readonly string infoCachePath = "info_cache.txt";
        private readonly TimeSpan cacheTimeout = TimeSpan.FromMinutes(1);

        public event EventHandler<UpdateProgressEventArgs> ProgressChanged;
        public event EventHandler<UpdateStatusEventArgs> StatusChanged;
        public event EventHandler<UpdateCompletedEventArgs> UpdateCompleted;

        public async Task<UpdateCheckResult> CheckForUpdatesAsync()
        {
            try
            {
                OnStatusChanged("Checking for updates...");
                
                string localVersion = GetLocalVersion();
                string serverVersion = await GetServerVersionAsync();

                bool updateRequired = !string.Equals(localVersion, serverVersion, StringComparison.OrdinalIgnoreCase);
                
                OnStatusChanged(updateRequired ? "Update available!" : "Up to date");
                
                return new UpdateCheckResult
                {
                    UpdateRequired = updateRequired,
                    LocalVersion = localVersion,
                    ServerVersion = serverVersion,
                    Success = true
                };
            }
            catch (Exception ex)
            {
                OnStatusChanged($"Update check failed: {ex.Message}");
                return new UpdateCheckResult
                {
                    UpdateRequired = false,
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<bool> DownloadAndInstallUpdateAsync()
        {
            try
            {
                OnStatusChanged("Downloading update...");
                
                using (var client = new WebClient())
                {
                    client.DownloadProgressChanged += (s, e) =>
                    {
                        OnProgressChanged(e.ProgressPercentage, $"Downloading... {e.ProgressPercentage}%");
                    };

                    await Task.Run(() => client.DownloadFile(serverZipUrl, localZipPath));
                }

                OnStatusChanged("Installing update...");
                OnProgressChanged(0, "Extracting files...");

                await Task.Run(() =>
                {
                    using (ZipFile zip = ZipFile.Read(localZipPath))
                    {
                        int totalFiles = zip.Count;
                        int extractedFiles = 0;

                        foreach (ZipEntry entry in zip)
                        {
                            entry.Extract(Directory.GetCurrentDirectory(), ExtractExistingFileAction.OverwriteSilently);
                            extractedFiles++;
                            int progress = (extractedFiles * 100) / totalFiles;
                            OnProgressChanged(progress, $"Extracting... {progress}%");
                        }
                    }
                });

                // Clean up
                if (File.Exists(localZipPath))
                    File.Delete(localZipPath);

                // Update local version
                string serverVersion = await GetServerVersionAsync();
                File.WriteAllText(localVersionPath, serverVersion);

                OnStatusChanged("Update completed successfully!");
                OnUpdateCompleted(true, null);
                return true;
            }
            catch (Exception ex)
            {
                OnStatusChanged($"Update failed: {ex.Message}");
                OnUpdateCompleted(false, ex.Message);
                return false;
            }
        }

        private string GetLocalVersion()
        {
            return File.Exists(localVersionPath) ? File.ReadAllText(localVersionPath).Trim() : "0.0.0";
        }

        private async Task<string> GetServerVersionAsync()
        {
            // Check cache first
            if (File.Exists(versionCachePath))
            {
                var cacheInfo = new FileInfo(versionCachePath);
                if (DateTime.Now - cacheInfo.LastWriteTime < cacheTimeout)
                {
                    return File.ReadAllText(versionCachePath).Trim();
                }
            }

            // Download fresh version info
            using (var client = new WebClient())
            {
                client.Headers.Add("User-Agent", "Project 404 Launcher 2025");
                string version = await Task.Run(() => client.DownloadString(serverVersionUrl).Trim());

                // Cache the result
                File.WriteAllText(versionCachePath, version);
                return version;
            }
        }

        public async Task<string> GetServerInfoAsync()
        {
            try
            {
                // Check cache first
                if (File.Exists(infoCachePath))
                {
                    var cacheInfo = new FileInfo(infoCachePath);
                    if (DateTime.Now - cacheInfo.LastWriteTime < cacheTimeout)
                    {
                        return File.ReadAllText(infoCachePath);
                    }
                }

                // Download fresh info from server
                using (var client = new WebClient())
                {
                    client.Headers.Add("User-Agent", "Project 404 Launcher 2025");
                    string info = await Task.Run(() => client.DownloadString(serverInfoUrl));

                    // Cache the result
                    File.WriteAllText(infoCachePath, info);
                    return info;
                }
            }
            catch (Exception ex)
            {
                // If we can't get server info, return a default message
                return "Welcome to Project 404!\n\nThis launcher will automatically check for updates and keep your game up to date.\n\nClick \"Launch Game\" to start playing!";
            }
        }

        private void OnProgressChanged(int percentage, string message)
        {
            ProgressChanged?.Invoke(this, new UpdateProgressEventArgs(percentage, message));
        }

        private void OnStatusChanged(string status)
        {
            StatusChanged?.Invoke(this, new UpdateStatusEventArgs(status));
        }

        private void OnUpdateCompleted(bool success, string errorMessage)
        {
            UpdateCompleted?.Invoke(this, new UpdateCompletedEventArgs(success, errorMessage));
        }
    }

    public class UpdateCheckResult
    {
        public bool UpdateRequired { get; set; }
        public string LocalVersion { get; set; }
        public string ServerVersion { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
    }

    public class UpdateProgressEventArgs : EventArgs
    {
        public int Percentage { get; }
        public string Message { get; }

        public UpdateProgressEventArgs(int percentage, string message)
        {
            Percentage = percentage;
            Message = message;
        }
    }

    public class UpdateStatusEventArgs : EventArgs
    {
        public string Status { get; }

        public UpdateStatusEventArgs(string status)
        {
            Status = status;
        }
    }

    public class UpdateCompletedEventArgs : EventArgs
    {
        public bool Success { get; }
        public string ErrorMessage { get; }

        public UpdateCompletedEventArgs(bool success, string errorMessage)
        {
            Success = success;
            ErrorMessage = errorMessage;
        }
    }
}
